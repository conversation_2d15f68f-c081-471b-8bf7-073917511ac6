# DaPlot 启动脚本使用指南

## 概述

`start_servers.py` 是 DaPlot 应用的智能启动脚本，提供了完整的服务器管理功能，包括自动端口分配、依赖检查、健康监控和优雅关闭。

## 功能特性

### 🚀 核心功能
- **自动端口冲突检测**：智能检测并分配可用端口
- **前端依赖检查**：验证关键 JavaScript 模块是否存在
- **服务器健康监控**：定期检查服务器状态
- **优雅进程管理**：安全启动和关闭服务器进程
- **详细日志记录**：完整的操作日志和错误追踪

### 📁 文件管理
- **运行时配置**：自动生成 `runtime-config.json` 配置文件
- **日志文件**：在 `logs/` 目录下保存详细的运行日志
- **自动清理**：关闭时清理临时文件

## 使用方法

### 基本启动
```bash
python start_servers.py
```

### 启动流程
1. **端口检查**：检测 8001（后端）和 3000（前端）端口可用性
2. **依赖验证**：检查前端关键文件（lib-loader.js、data-persistence.js、page-bridge.js）
3. **后端启动**：启动 FastAPI 后端服务器
4. **健康检查**：验证后端 API 可访问性
5. **前端启动**：启动静态文件服务器
6. **监控模式**：进入持续监控状态

## 输出说明

### 状态指示器
- ✅ **成功**：操作成功完成
- ⚠️ **警告**：非致命问题，但需要注意
- ❌ **错误**：严重问题，可能影响功能
- 🔍 **检查**：正在进行状态检查
- 🔄 **监控**：持续监控状态

### 访问地址
启动成功后，脚本会显示以下访问地址：
- **首页**：http://localhost:3000/index.html
- **数据可视化**：http://localhost:3000/visualization.html
- **数据预测**：http://localhost:3000/prediction.html
- **数据集成**：http://localhost:3000/data_integrated.html
- **API 文档**：http://localhost:8001/docs

## 配置文件

### runtime-config.json
启动时自动生成的配置文件，包含：
```json
{
  "backend_port": 8001,
  "frontend_port": 3000,
  "api_base_url": "http://localhost:8001",
  "frontend_url": "http://localhost:3000",
  "startup_time": "2024-12-24T10:30:00.000000",
  "version": "1.0.0"
}
```

## 日志系统

### 日志文件位置
- 目录：`logs/`
- 文件名格式：`daplot_YYYYMMDD_HHMMSS.log`
- 编码：UTF-8

### 日志级别
- **INFO**：正常操作信息
- **WARNING**：警告信息
- **ERROR**：错误信息

## 健康监控

### 监控机制
- **启动检查**：服务器启动后立即验证可访问性
- **定期检查**：每 30 秒检查一次服务器健康状态
- **异常检测**：监控进程意外终止

### 健康检查端点
- **后端**：`/docs` 端点
- **前端**：根路径 `/`

## 故障排除

### 常见问题

#### 端口被占用
```
❌ 错误: 无法找到可用的后端端口
```
**解决方案**：关闭占用端口的程序，或等待脚本自动分配其他端口

#### 依赖文件缺失
```
⚠️ 缺少前端依赖文件: lib-loader.js, data-persistence.js
```
**解决方案**：确保前端目录包含所有必需的 JavaScript 模块

#### 后端启动失败
```
❌ 错误: 未找到 uv 命令
```
**解决方案**：安装 uv 包管理器：`pip install uv`

#### 健康检查失败
```
⚠️ 后端服务器健康检查失败
```
**解决方案**：检查后端服务器日志，确认 API 服务正常运行

## 优雅关闭

### 关闭方式
- **Ctrl+C**：发送中断信号
- **SIGTERM**：系统终止信号

### 关闭流程
1. 接收停止信号
2. 优雅终止后端进程（5秒超时）
3. 优雅终止前端进程（5秒超时）
4. 清理运行时配置文件
5. 记录关闭日志

### 强制终止
如果进程在 5 秒内未能优雅退出，脚本会强制终止进程。

## 最佳实践

1. **定期检查日志**：查看 `logs/` 目录下的日志文件
2. **监控资源使用**：注意服务器的 CPU 和内存使用情况
3. **备份配置**：重要配置文件应定期备份
4. **更新依赖**：保持前端依赖文件的最新版本

## 技术细节

### 依赖要求
- Python 3.7+
- uv 包管理器
- FastAPI 后端应用
- 静态文件服务器支持

### 架构设计
- **模块化设计**：功能分离，易于维护
- **异常处理**：完善的错误处理机制
- **资源管理**：自动清理临时资源
- **信号处理**：支持优雅关闭

---

*更新时间：2024年12月24日*
*版本：1.0.0*