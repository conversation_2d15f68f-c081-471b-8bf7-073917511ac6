<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DaPlot - 可视化绘图</title>
    <!-- 快速修复脚本 -->
    <script src="assets/js/lib-loader.js"></script>
    <script src="assets/js/data-persistence.js"></script>
    <script src="assets/js/page-bridge.js"></script>
    <script>
        // Plotly备用加载机制
        function loadPlotlyFallback() {
            console.warn('⚠️ 主要Plotly CDN加载失败，尝试备用CDN...');
            
            // 移除失败的脚本
            const failedScript = document.getElementById('plotlyMainScript');
            if (failedScript) {
                failedScript.remove();
            }
            
            // 尝试备用CDN列表
            const fallbackCDNs = [
                'https://unpkg.com/plotly.js@2.26.0/dist/plotly.min.js',
                'https://cdnjs.cloudflare.com/ajax/libs/plotly.js/2.26.0/plotly.min.js',
                'https://cdn.jsdelivr.net/npm/plotly.js@2.26.0/dist/plotly.min.js'
            ];
            
            let currentIndex = 0;
            
            function tryNextCDN() {
                if (currentIndex >= fallbackCDNs.length) {
                    console.error('❌ 所有Plotly CDN都加载失败');
                    if (typeof updateLoadingText === 'function') {
                        updateLoadingText('Plotly加载失败，请检查网络连接');
                    }
                    return;
                }
                
                const script = document.createElement('script');
                script.src = fallbackCDNs[currentIndex];
                script.id = `plotlyFallback${currentIndex}`;
                
                script.onload = function() {
                    console.log(`✅ 备用CDN加载成功: ${fallbackCDNs[currentIndex]}`);
                };
                
                script.onerror = function() {
                    console.warn(`⚠️ 备用CDN ${currentIndex + 1} 加载失败: ${fallbackCDNs[currentIndex]}`);
                    script.remove();
                    currentIndex++;
                    setTimeout(tryNextCDN, 1000); // 1秒后尝试下一个
                };
                
                document.head.appendChild(script);
                console.log(`🔄 尝试备用CDN ${currentIndex + 1}: ${fallbackCDNs[currentIndex]}`);
            }
            
            // 延迟1秒后开始尝试备用CDN
            setTimeout(tryNextCDN, 1000);
        }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #333;
            background: #f8f9fa;
        }

        /* 加载覆盖层 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(248, 249, 250, 0.95);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            font-size: 14px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e9ecef;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #6c757d;
            margin-bottom: 10px;
        }

        .loading-tip {
            color: #868e96;
            font-size: 12px;
            text-align: center;
            max-width: 300px;
        }

        .page-wrapper {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            flex-shrink: 0;
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            transition: width 0.3s ease;
            padding: 15px;
            display: flex;
            flex-direction: column;
            font-size: 12px;
            position: relative;
        }

        .sidebar.collapsed {
            width: 50px;
            padding: 15px 5px;
        }

        .sidebar.collapsed .sidebar-content {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 15px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 5px 8px;
            cursor: pointer;
            font-size: 12px;
            z-index: 1000;
        }

        .sidebar-toggle:hover {
            background: #0056b3;
        }

        .sidebar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            color: #333;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .sidebar-header h1 {
            font-size: 1.3rem;
            margin: 0;
            font-weight: 600;
        }

        .content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .navbar {
            background: #343a40;
            color: white;
            padding: 10px 15px;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
        }

        .control-panel {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            font-size: 11px;
        }

        .main-content {
            width: 100%;
            padding: 15px;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 15px;
        }

        .nav-bar {
            background: none;
            border-radius: 0;
            padding: 0;
            margin-bottom: 0;
            box-shadow: none;
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex-grow: 1;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            color: #666;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.8);
            border: none;
            cursor: pointer;
        }

        .nav-btn:hover {
            background: rgba(74, 144, 226, 0.1);
            color: #4a90e2;
            transform: translateX(3px);
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            color: white;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        .nav-btn i {
            margin-right: 10px;
            font-size: 16px;
            width: 16px;
            text-align: center;
        }

        .controls-panel {
            background: white;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            height: fit-content;
            border: 1px solid #e9ecef;
        }

        .chart-area {
            flex: 1;
            background: white;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
            min-height: 600px;
        }

        .toolbar {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .toolbar-btn {
            padding: 6px 12px;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            background: white;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }

        .toolbar-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .section {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
            border: 1px solid #e9ecef;
        }

        .section h3 {
            margin-bottom: 12px;
            color: #495057;
            font-size: 1rem;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 10px;
        }

        .form-group label {
            display: block;
            margin-bottom: 3px;
            color: #495057;
            font-weight: 500;
            font-size: 11px;
        }

        .form-control {
            width: 100%;
            padding: 5px 8px;
            border: 1px solid #ced4da;
            border-radius: 3px;
            font-size: 11px;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #4a90e2;
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }

        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .tag {
            background: #e9ecef;
            color: #495057;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .tag:hover {
            background: #dee2e6;
        }

        .tag.selected {
            background: #4a90e2;
            color: white;
            border-color: #357abd;
        }

        .btn {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            transition: background 0.3s ease;
            width: 100%;
        }

        .btn:hover {
            background: #357abd;
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .status-message {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            display: none;
        }

        .status-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .chart-toolbar {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .toolbar-btn {
            background: white;
            border: 1px solid #ced4da;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .toolbar-btn:hover {
            background: #e9ecef;
        }

        .toolbar-btn.active {
            background: #4a90e2;
            color: white;
            border-color: #357abd;
        }

        .curve-control-panel {
            display: none;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .curve-control-panel h4 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 0.9rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            user-select: none;
        }

        .curve-control-panel h4:hover {
            color: #007bff;
        }

        .curve-control-toggle {
            font-size: 0.8rem;
            transition: transform 0.2s ease;
        }

        .curve-control-toggle.collapsed {
            transform: rotate(-90deg);
        }

        .curve-control-content {
            max-height: 300px;
            overflow-y: auto;
            transition: max-height 0.3s ease;
        }

        .curve-control-content.collapsed {
            max-height: 0;
            overflow: hidden;
        }

        .curve-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            margin-bottom: 8px;
            transition: background-color 0.2s ease;
        }

        .curve-item:hover {
            background: #f8f9fa;
        }

        .curve-name {
            flex: 1;
            font-size: 0.8rem;
            color: #495057;
            font-weight: 500;
            min-width: 120px;
        }

        .curve-color-picker {
            width: 30px;
            height: 25px;
            border: 1px solid #ced4da;
            border-radius: 3px;
            cursor: pointer;
            padding: 0;
            transition: border-color 0.2s ease;
        }

        .curve-color-picker:hover {
            border-color: #007bff;
        }

        .curve-marker-select {
            width: 100px;
            padding: 3px 6px;
            border: 1px solid #ced4da;
            border-radius: 3px;
            font-size: 0.75rem;
            transition: border-color 0.2s ease;
        }

        .curve-marker-select:hover,
        .curve-marker-select:focus {
            border-color: #007bff;
            outline: none;
        }

        .curve-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .curve-control-buttons {
            display: flex;
            gap: 8px;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #dee2e6;
        }

        .curve-control-buttons .btn {
            font-size: 0.8rem;
            padding: 4px 8px;
        }

        /* 灵活表头选择管理器样式 */
        .flexible-header-manager {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
        }

        .manager-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }

        .manager-header span {
            font-size: 11px;
            color: #495057;
            font-weight: 500;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 11px;
            width: auto;
        }

        .flexible-header-list {
            min-height: 20px;
        }

        .flexible-header-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 8px;
            position: relative;
        }

        .flexible-header-item:last-child {
            margin-bottom: 0;
        }

        .header-item-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 8px;
        }

        .header-item-controls select {
            flex: 1;
            min-width: 120px;
        }

        .remove-header-btn {
            background: #dc3545;
            border-color: #dc3545;
            color: white;
            padding: 4px 8px;
            font-size: 10px;
            border-radius: 3px;
            cursor: pointer;
            border: none;
            transition: background 0.3s ease;
        }

        .remove-header-btn:hover {
            background: #c82333;
        }

        .header-values-container {
            margin-top: 8px;
        }

        .header-values-label {
            font-size: 10px;
            color: #6c757d;
            margin-bottom: 4px;
            display: block;
        }

        .global-indicator {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border: 1px dashed #dee2e6;
            border-radius: 4px;
            margin-top: 10px;
        }

        /* 图表上方控制区域样式 */
        .chart-controls {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            padding: 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            flex-wrap: wrap;
            align-items: center;
        }

        .style-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .style-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .style-group label {
            font-size: 0.8rem;
            margin: 0;
            white-space: nowrap;
        }

        .style-group select,
        .style-group input {
            font-size: 0.8rem;
            padding: 2px 6px;
            border: 1px solid #ced4da;
            border-radius: 3px;
            min-width: 80px;
        }

        .style-group input[type="number"] {
            width: 60px;
        }

        .apply-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .apply-buttons .btn {
            font-size: 0.8rem;
            padding: 4px 12px;
        }

        #plotDiv {
            width: 100%;
            height: 500px;
        }

        .no-data-message {
            text-align: center;
            color: #6c757d;
            padding: 50px;
            font-style: italic;
        }

        .flexible-selector {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .flexible-selector h4 {
            color: #1565c0;
            margin-bottom: 10px;
            font-size: 13px;
        }

        .header-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .header-tag {
            background: #e3f2fd;
            color: #1565c0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #bbdefb;
        }

        .header-tag:hover {
            background: #bbdefb;
        }

        .header-tag.selected {
            background: #1976d2;
            color: white;
            border-color: #1565c0;
        }
    </style>
    <!-- DaPlot 快速修复脚本 -->
    <script src="/assets/js/lib-loader.js"></script>
    <script src="/assets/js/data-persistence.js"></script>
    <script src="/assets/js/page-bridge.js"></script>
</head>
<body>
    <!-- 加载覆盖层 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载可视化页面...</div>
        <div class="loading-tip">正在加载Plotly图表库和文件数据，请稍候</div>
    </div>

    <div class="page-wrapper">
        <div class="sidebar" id="sidebar">
            <button class="sidebar-toggle" onclick="toggleSidebar()" title="收起/展开侧边栏">◀</button>
            <div class="sidebar-content">
                <div class="sidebar-header">
                    <h1>DaPlot</h1>
                </div>
                <nav class="nav-bar">
                <a href="index.html" class="nav-btn">
                    <i>🏠</i>
                    <span>首页</span>
                </a>
                <a href="data_integrated.html" class="nav-btn">
                    <i>📊</i>
                    <span>数据操作</span>
                </a>
                <a href="visualization.html" class="nav-btn active">
                    <i>📈</i>
                    <span>可视化绘图</span>
                </a>
                <a href="prediction.html" class="nav-btn">
                    <i>🔮</i>
                    <span>数据预测</span>
                </a>
                <a href="donate.html" class="nav-btn">
                    <i>❤️</i>
                    <span>捐赠支持</span>
                </a>
            </nav>

            </div>
        </div>

        <div class="content">
            <div class="main-content">
                <div class="controls-panel">
                    <div class="status-message" id="statusMessage"></div>

                    <div class="section">
                        <h3 style="font-size: 13px; margin-bottom: 8px;">📁 文件选择</h3>
                        <div class="form-group">
                            <label for="fileSelector">选择数据文件:</label>
                            <select id="fileSelector" class="form-control" onchange="switchFile()">
                                <option value="">请选择数据文件</option>
                            </select>
                        </div>
                        <div id="currentFileInfo" style="font-size: 10px; color: #666; margin-top: 3px; padding: 3px; background: #f8f9fa; border-radius: 2px;"></div>
                    </div>

                    <div class="section">
                        <h3 style="font-size: 13px; margin-bottom: 8px;">🎯 灵活表头选择管理</h3>
                        <div class="flexible-header-manager">
                            <div class="manager-header">
                                <span>当前筛选条件 (不选择则默认全局)</span>
                                <button class="btn btn-small" onclick="addFlexibleHeaderSelector()" style="background-color: #28a745; border-color: #28a745; font-size: 11px; padding: 4px 8px;">+ 添加筛选</button>
                            </div>
                            <div id="flexibleHeaderList" class="flexible-header-list">
                                <!-- 动态生成的灵活表头选择器将在这里显示 -->
                            </div>
                            <div class="global-indicator" id="globalIndicator" style="display: block;">
                                <span style="color: #6c757d; font-size: 11px;">📊 当前为全局模式 - 显示所有数据</span>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <h3 style="font-size: 13px; margin-bottom: 8px;">📊 坐标轴设置</h3>
                        <div class="form-group">
                            <label for="xAxis">X轴:</label>
                            <select id="xAxis" class="form-control">
                                <option value="">请选择X轴变量</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="yAxis">Y轴:</label>
                            <select id="yAxis" class="form-control">
                                <option value="">请选择Y轴变量</option>
                            </select>
                        </div>
                    </div>

                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <button class="btn" id="generateChart" onclick="generateChart()" disabled>
                            生成图表
                        </button>
                    </div>
                </div>

                <div class="chart-area">
                    <div id="chartToolbar" class="toolbar">
                        <button class="toolbar-btn" onclick="resetZoom()">🔍 重置缩放</button>
                        <button class="toolbar-btn" onclick="downloadPNG()">📷 保存PNG</button>
                        <button class="toolbar-btn" onclick="downloadSVG()">🎨 保存SVG</button>
                    </div>

                    <!-- 图表控制区域 -->
                    <div id="chartControls" class="chart-controls" style="display: none;">
                        <div class="style-controls">
                            <!-- 标题和轴标签设置 -->
                            <div class="style-group">
                                <label for="chartTitle">图表标题:</label>
                                <input type="text" id="chartTitle" placeholder="请输入图表标题" style="min-width: 150px;">
                            </div>

                            <div class="style-group">
                                <label for="xAxisTitle">X轴标题:</label>
                                <input type="text" id="xAxisTitle" placeholder="请输入X轴标题" style="min-width: 120px;">
                            </div>

                            <div class="style-group">
                                <label for="yAxisTitle">Y轴标题:</label>
                                <input type="text" id="yAxisTitle" placeholder="请输入Y轴标题" style="min-width: 120px;">
                            </div>

                            <div class="style-group">
                                <label for="legendPosition">图例:</label>
                                <select id="legendPosition">
                                    <option value="outside-right">右侧外部</option>
                                    <option value="inside-topright">内部右上</option>
                                    <option value="inside-topleft">内部左上</option>
                                    <option value="inside-bottomright">内部右下</option>
                                    <option value="inside-bottomleft">内部左下</option>
                                    <option value="top">顶部</option>
                                    <option value="bottom">底部</option>
                                </select>
                            </div>

                            <div class="style-group">
                                <label for="lineWidth">线条:</label>
                                <input type="number" id="lineWidth" min="1" max="8" value="2" step="1">
                            </div>

                            <div class="style-group">
                                <label for="markerSize">标记:</label>
                                <input type="number" id="markerSize" min="3" max="15" value="6" step="1">
                            </div>

                            <div class="style-group">
                                <label for="markerStyle">样式:</label>
                                <select id="markerStyle">
                                    <option value="circle">圆形 ●</option>
                                    <option value="square">方形 ■</option>
                                    <option value="diamond">菱形 ♦</option>
                                    <option value="triangle-up">上三角 ▲</option>
                                    <option value="triangle-down">下三角 ▼</option>
                                    <option value="cross">十字 +</option>
                                    <option value="x">X形 ×</option>
                                </select>
                            </div>

                            <div class="style-group">
                                <label for="colorScheme">配色:</label>
                                <select id="colorScheme">
                                    <option value="default">默认配色</option>
                                    <option value="warm">暖色调</option>
                                    <option value="cool">冷色调</option>
                                    <option value="pastel">柔和色</option>
                                    <option value="bright">鲜艳色</option>
                                    <option value="monochrome">单色调</option>
                                </select>
                            </div>
                        </div>

                        <div class="apply-buttons">
                            <button class="btn" onclick="applyStyleToChart()" style="background-color: #17a2b8; border-color: #17a2b8;">
                                应用样式
                            </button>
                        </div>
                    </div>

                    <!-- 曲线控制面板 -->
                    <div id="curveControlPanel" class="curve-control-panel">
                        <h4 onclick="toggleCurveControlPanel()">
                            🎨 曲线样式控制
                            <span class="curve-control-toggle" id="curveControlToggle">▼</span>
                        </h4>
                        <div id="curveControlContent" class="curve-control-content">
                            <div id="curveControlList">
                                <!-- 动态生成的曲线控制项 -->
                            </div>
                            <div class="curve-control-buttons">
                                <button class="btn" onclick="applyCurveStyles()" style="background-color: #28a745; border-color: #28a745;">
                                    应用曲线样式
                                </button>
                                <button class="btn" onclick="resetAllCurveStyles()" style="background-color: #6c757d; border-color: #6c757d;">
                                    重置样式
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="plotDiv">
                        <div class="no-data-message">
                            <h3 style="font-size: 13px; margin-bottom: 8px;">📈 图表区域</h3>
                            <p>请先选择项目、状态变量和坐标轴，然后点击"生成图表"</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let fileId = null;
        let headers = [];
        let flexibleHeaderSelectors = []; // 灵活表头选择器数组
        let nextSelectorId = 1; // 下一个选择器的ID
        let allData = [];
        let currentPlot = null;

        // 等待 Plotly 加载完成（使用统一的lib-loader）
        function waitForPlotly() {
            return new Promise(async (resolve, reject) => {
                console.log('🔍 开始检查Plotly加载状态...');
                
                // 如果已经加载，直接返回
                if (typeof Plotly !== 'undefined') {
                    console.log('✅ Plotly已经加载完成');
                    resolve();
                    return;
                }
                
                try {
                    // 使用lib-loader加载Plotly（优先本地，回退CDN）
                    if (window.libLoader) {
                        console.log('⏳ 使用lib-loader加载Plotly...');
                        await window.libLoader.loadPlotly();
                        
                        // 再次检查是否加载成功
                        if (typeof Plotly !== 'undefined') {
                            console.log('✅ Plotly通过lib-loader加载成功');
                            resolve();
                        } else {
                            throw new Error('lib-loader完成但Plotly仍未定义');
                        }
                    } else {
                        // 备用方案：直接等待
                        console.warn('⚠️ lib-loader不可用，使用备用等待机制...');
                        let attempts = 0;
                        const maxAttempts = 200; // 10秒超时
                        
                        const checkInterval = setInterval(() => {
                            attempts++;
                            
                            if (typeof Plotly !== 'undefined') {
                                clearInterval(checkInterval);
                                console.log('✅ Plotly加载成功（备用机制）');
                                resolve();
                            } else if (attempts >= maxAttempts) {
                                clearInterval(checkInterval);
                                reject(new Error('Plotly加载超时，请刷新页面重试'));
                            }
                        }, 50);
                    }
                } catch (error) {
                    console.error('❌ Plotly加载失败:', error);
                    reject(error);
                }
            });
        }

        // 更新加载提示
        function updateLoadingText(text) {
            const loadingText = document.querySelector('.loading-text');
            if (loadingText) {
                loadingText.textContent = text;
            }
        }

        // 隐藏加载覆盖层
        function hideLoadingOverlay() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.opacity = '0';
                setTimeout(() => {
                    overlay.style.display = 'none';
                }, 300);
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                console.log('=== 开始初始化可视化页面 ===');
                
                // 更新加载提示
                updateLoadingText('正在加载本地数据...');

                // 先从localStorage加载数据
                loadDataFromStorage();
                console.log('✓ localStorage数据加载完成');

                // 更新加载提示
                updateLoadingText('正在加载Plotly图表库（优先本地）...');

                // 等待 Plotly 加载完成
                await waitForPlotly();
                console.log('✓ Plotly 已加载完成（优先本地，回退CDN）');

                // 更新加载提示
                updateLoadingText('正在初始化界面...');

                // 初始化样式控制
                initializeStyleControls();

                // 初始化表单验证状态
                checkFormValidity();

                // 更新加载提示
                updateLoadingText('正在加载文件列表...');

                // 加载文件列表（这会选中localStorage中的fileId）
                await loadFileList();
                console.log('✓ 文件列表加载完成');

                // 如果有fileId，初始化可视化
                if (fileId && headers.length > 0) {
                    updateLoadingText('正在初始化数据可视化...');
                    await initializeVisualization();
                    console.log('✓ 可视化初始化完成');
                    showMessage('页面加载完成，可以开始制作图表', 'success');
                } else {
                    console.log('! 没有可用的文件数据');
                    showMessage('请选择数据文件或先在数据页面上传数据', 'info');
                }

                // 隐藏加载覆盖层
                hideLoadingOverlay();
                console.log('=== 页面初始化完成 ===');

            } catch (error) {
                console.error('页面初始化失败:', error);
                updateLoadingText('加载失败: ' + error.message);
                showMessage('页面初始化失败: ' + error.message, 'error');
                
                // 即使失败也要隐藏加载覆盖层
                setTimeout(() => {
                    hideLoadingOverlay();
                }, 2000);
            }
        });

        // 初始化样式控制
        function initializeStyleControls() {
            // 样式控制已改为数字输入框，无需额外初始化
        }

        // 获取配色方案
        function getColorScheme(scheme) {
            const colorSchemes = {
                'default': ['#4a90e2', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#e67e22'],
                'warm': ['#ff6b6b', '#ffa726', '#ff7043', '#d32f2f', '#f57c00', '#e65100', '#bf360c', '#8d6e63'],
                'cool': ['#42a5f5', '#26c6da', '#66bb6a', '#29b6f6', '#26a69a', '#5c6bc0', '#7e57c2', '#ab47bc'],
                'pastel': ['#ffcdd2', '#f8bbd9', '#e1bee7', '#d1c4e9', '#c5cae9', '#bbdefb', '#b3e5fc', '#b2dfdb'],
                'bright': ['#ff1744', '#ff6d00', '#76ff03', '#00e676', '#00bcd4', '#3f51b5', '#9c27b0', '#e91e63'],
                'monochrome': ['#212121', '#424242', '#616161', '#757575', '#9e9e9e', '#bdbdbd', '#e0e0e0', '#f5f5f5']
            };
            return colorSchemes[scheme] || colorSchemes['default'];
        }

        // 获取图例配置
        function getLegendConfig(position) {
            const legendConfigs = {
                'outside-right': { x: 1.02, y: 1, xanchor: 'left', yanchor: 'top' },
                'inside-topright': { x: 0.98, y: 0.98, xanchor: 'right', yanchor: 'top' },
                'inside-topleft': { x: 0.02, y: 0.98, xanchor: 'left', yanchor: 'top' },
                'inside-bottomright': { x: 0.98, y: 0.02, xanchor: 'right', yanchor: 'bottom' },
                'inside-bottomleft': { x: 0.02, y: 0.02, xanchor: 'left', yanchor: 'bottom' },
                'top': { x: 0.5, y: 1.02, xanchor: 'center', yanchor: 'bottom', orientation: 'h' },
                'bottom': { x: 0.5, y: -0.1, xanchor: 'center', yanchor: 'top', orientation: 'h' }
            };
            return legendConfigs[position] || legendConfigs['outside-right'];
        }

        async function loadFileList() {
             try {
                 console.log('🔄 开始加载文件列表...');
                 console.log('📡 API端点:', window.pageBridge.getApiUrl('/files'));
                 
                 const startTime = Date.now();
                 const response = await fetch(window.pageBridge.getApiUrl('/files'), {
                     method: 'GET',
                     headers: {
                         'Content-Type': 'application/json',
                         'Accept': 'application/json'
                     },
                     cache: 'no-cache'
                 });
                 
                 const requestTime = Date.now() - startTime;
                 console.log(`📡 请求完成 - 状态: ${response.status}, 耗时: ${requestTime}ms`);
                 
                 if (!response.ok) {
                     const errorText = await response.text();
                     console.error('❌ 请求失败详情:', {
                         status: response.status,
                         statusText: response.statusText,
                         errorText: errorText
                     });
                     throw new Error(`HTTP ${response.status}: ${response.statusText}${errorText ? ' - ' + errorText : ''}`);
                 }

                 const responseText = await response.text();
                 console.log('📄 原始响应内容:', responseText);
                 
                 let data;
                 try {
                     data = JSON.parse(responseText);
                 } catch (parseError) {
                     console.error('❌ JSON解析失败:', parseError);
                     throw new Error('服务器返回的数据格式不正确');
                 }
                 
                 console.log('📋 解析后的数据:', data);
                 console.log('📊 数据结构分析:', {
                     hasFiles: 'files' in data,
                     filesType: typeof data.files,
                     isArray: Array.isArray(data.files),
                     length: data.files ? data.files.length : 'N/A'
                 });

                 const fileSelector = document.getElementById('fileSelector');
                 if (!fileSelector) {
                     console.error('❌ 找不到文件选择器元素');
                     throw new Error('页面元素缺失');
                 }

                 // 清空现有选项
                 fileSelector.innerHTML = '<option value="">请选择数据文件</option>';

                 // 添加文件选项
                 if (data.files && Array.isArray(data.files) && data.files.length > 0) {
                     console.log(`✅ 找到 ${data.files.length} 个文件`);
                     data.files.forEach((file, index) => {
                         console.log(`📁 处理文件 ${index + 1}:`, file);
                         
                         if (!file.file_id) {
                             console.warn('⚠️ 文件缺少file_id:', file);
                             return;
                         }
                         
                         const option = document.createElement('option');
                         option.value = file.file_id;
                         const displayName = file.filename || file.original_filename || `文件 ${file.file_id.substring(0, 8)}...`;
                         const sizeInfo = (file.rows && file.columns) ? ` (${file.rows}行×${file.columns}列)` : '';
                         option.textContent = `${displayName}${sizeInfo}`;
                         fileSelector.appendChild(option);
                     });
                 } else {
                     console.warn('⚠️ 文件列表为空或格式不正确:', data);
                     const option = document.createElement('option');
                     option.value = '';
                     option.textContent = '暂无可用文件 - 请先在数据页面上传文件';
                     option.disabled = true;
                     fileSelector.appendChild(option);
                     
                     if (fileId) {
                         console.log('🧹 清除无效的localStorage数据');
                         showMessage('未找到之前选择的文件，请重新上传数据', 'warning');
                         localStorage.removeItem('fileId');
                         localStorage.removeItem('headers');
                         fileId = null;
                         headers = [];
                     }
                     return;
                 }

                 // 如果有当前文件ID，选中它
                 if (fileId) {
                     console.log('🔍 检查localStorage中的fileId:', fileId);
                     const foundFile = data.files.find(file => file.file_id === fileId);
                     if (foundFile) {
                         fileSelector.value = fileId;
                         updateCurrentFileInfo();
                         console.log('✅ 已选中文件:', foundFile.filename || foundFile.original_filename);
                     } else {
                         console.warn('⚠️ localStorage中的fileId在文件列表中未找到:', fileId);
                         showMessage('之前选择的文件已不存在，请重新选择', 'warning');
                         localStorage.removeItem('fileId');
                         localStorage.removeItem('headers');
                         fileId = null;
                         headers = [];
                     }
                 }

                 console.log(`✅ 文件列表加载完成，共 ${data.files.length} 个文件`);

             } catch (error) {
                 console.error('❌ 加载文件列表失败:', error);
                 
                 // 显示更详细的错误信息
                 if (error.message.includes('Failed to fetch')) {
                     console.error('🔌 网络连接问题: 无法连接到后端服务');
                     showMessage('无法连接到后端服务 (http://localhost:8001)，请确认服务是否正常运行', 'error');
                 } else if (error.message.includes('JSON')) {
                     console.error('📄 数据格式问题:', error.message);
                     showMessage('服务器返回数据格式错误: ' + error.message, 'error');
                 } else {
                     console.error('🚫 其他错误:', error.message);
                     showMessage('加载文件列表失败: ' + error.message, 'error');
                 }
                 
                 // 在错误情况下也要设置基本的选项
                 const fileSelector = document.getElementById('fileSelector');
                 if (fileSelector) {
                     fileSelector.innerHTML = '<option value="">加载失败 - 请刷新页面重试</option>';
                 }
                 
                 throw error;
             }
         }

        async function switchFile() {
            const fileSelector = document.getElementById('fileSelector');
            const selectedFileId = fileSelector.value;

            if (!selectedFileId) {
                fileId = null;
                headers = [];
                clearVisualization();
                return;
            }

            try {
                showMessage('正在切换文件...', 'info');

                // 获取文件数据
                const response = await fetch(window.pageBridge.getApiUrl(`/file/${selectedFileId}`));
                if (!response.ok) {
                    throw new Error('获取文件数据失败');
                }

                const fileData = await response.json();

                // 更新全局变量
                fileId = selectedFileId;
                headers = fileData.headers || [];

                // 保存到localStorage
                localStorage.setItem('fileId', fileId);
                localStorage.setItem('headers', JSON.stringify(headers));

                // 更新界面
                updateCurrentFileInfo();
                populateAxisSelectors();
                initializeFlexibleHeaderManager();
                clearVisualization();

                // 重新初始化可视化
                if (fileId) {
                    await initializeVisualization();
                }

                showMessage('文件切换成功', 'success');

            } catch (error) {
                console.error('切换文件失败:', error);
                showMessage('切换文件失败: ' + error.message, 'error');
            }
        }

        function updateCurrentFileInfo() {
            const fileSelector = document.getElementById('fileSelector');
            const currentFileInfo = document.getElementById('currentFileInfo');

            if (fileSelector.value && headers.length > 0) {
                const selectedOption = fileSelector.options[fileSelector.selectedIndex];
                currentFileInfo.textContent = `已选择: ${selectedOption.textContent}`;
                currentFileInfo.style.color = '#28a745';
            } else {
                currentFileInfo.textContent = '未选择文件';
                currentFileInfo.style.color = '#6c757d';
            }
        }

        function clearVisualization() {
            // 清空灵活表头选择器
            flexibleHeaderSelectors = [];
            nextSelectorId = 1;
            allData = [];

            // 清空界面
            document.getElementById('xAxis').value = '';
            document.getElementById('yAxis').value = '';
            
            // 重新渲染灵活表头选择器
            renderFlexibleHeaderSelectors();
            updateGlobalIndicator();

            // 清空图表
            const plotDiv = document.getElementById('plotDiv');
            plotDiv.innerHTML = `
                <div class="no-data-message">
                    <h3 style="font-size: 13px; margin-bottom: 8px;">📈 图表区域</h3>
                    <p>请先选择坐标轴，然后点击"生成图表"</p>
                </div>
            `;

            document.getElementById('chartToolbar').style.display = 'none';
            document.getElementById('chartControls').style.display = 'none';
            document.getElementById('curveControlPanel').style.display = 'none';
            document.getElementById('generateChart').disabled = true;
        }

        function loadDataFromStorage() {
            console.log('开始从localStorage加载数据...');
            
            fileId = localStorage.getItem('fileId');
            const headersStr = localStorage.getItem('headers');

            console.log('localStorage数据:');
            console.log('- fileId:', fileId);
            console.log('- headersStr:', headersStr);

            if (fileId && headersStr) {
                try {
                    headers = JSON.parse(headersStr);
                    console.log('- 解析后的headers:', headers);
                    
                    if (Array.isArray(headers) && headers.length > 0) {
                        console.log(`成功加载 ${headers.length} 个表头`);
                        populateAxisSelectors();
                        initializeFlexibleHeaderManager();
                    } else {
                        console.warn('headers数组为空或格式不正确');
                        headers = [];
                        showMessage('表头数据格式不正确，请重新选择文件', 'warning');
                    }
                } catch (error) {
                    console.error('解析headers失败:', error);
                    headers = [];
                    showMessage('解析表头数据失败: ' + error.message, 'error');
                }
            } else {
                console.log('localStorage中没有完整的文件数据');
                fileId = null;
                headers = [];
                
                if (!fileId && !headersStr) {
                    console.log('这是首次访问或数据已清空');
                } else if (!fileId) {
                    console.warn('缺少fileId');
                    showMessage('文件ID丢失，请重新选择文件', 'warning');
                } else if (!headersStr) {
                    console.warn('缺少headers数据');
                    showMessage('表头数据丢失，请重新选择文件', 'warning');
                }
            }
            
            console.log('localStorage数据加载完成:', { fileId, headersCount: headers.length });
        }

        function populateAxisSelectors() {
            const xAxisSelect = document.getElementById('xAxis');
            const yAxisSelect = document.getElementById('yAxis');

            // 清空现有选项
            xAxisSelect.innerHTML = '<option value="">请选择X轴变量</option>';
            yAxisSelect.innerHTML = '<option value="">请选择Y轴变量</option>';

            // 添加列选项
            headers.forEach(header => {
                const xOption = document.createElement('option');
                xOption.value = header;
                xOption.textContent = header;
                xAxisSelect.appendChild(xOption);

                const yOption = document.createElement('option');
                yOption.value = header;
                yOption.textContent = header;
                yAxisSelect.appendChild(yOption);
            });

            // 添加变化监听器
            xAxisSelect.addEventListener('change', checkFormValidity);
            yAxisSelect.addEventListener('change', checkFormValidity);
        }

        // 灵活表头选择管理功能
        function addFlexibleHeaderSelector() {
            const selectorId = nextSelectorId++;
            const selector = {
                id: selectorId,
                column: '',
                values: []
            };
            
            flexibleHeaderSelectors.push(selector);
            renderFlexibleHeaderSelectors();
            updateGlobalIndicator();
        }

        function removeFlexibleHeaderSelector(selectorId) {
            flexibleHeaderSelectors = flexibleHeaderSelectors.filter(s => s.id !== selectorId);
            renderFlexibleHeaderSelectors();
            updateGlobalIndicator();
            checkFormValidity();
        }

        function renderFlexibleHeaderSelectors() {
            const container = document.getElementById('flexibleHeaderList');
            container.innerHTML = '';

            flexibleHeaderSelectors.forEach(selector => {
                const item = document.createElement('div');
                item.className = 'flexible-header-item';
                item.innerHTML = `
                    <div class="header-item-controls">
                        <select class="form-control" onchange="updateSelectorColumn(${selector.id}, this.value)">
                            <option value="">请选择表头列</option>
                            ${headers.map(header => 
                                `<option value="${header}" ${selector.column === header ? 'selected' : ''}>${header}</option>`
                            ).join('')}
                        </select>
                        <button class="remove-header-btn" onclick="removeFlexibleHeaderSelector(${selector.id})">删除</button>
                    </div>
                    <div class="header-values-container">
                        <span class="header-values-label">选择筛选值:</span>
                        <div id="headerValues_${selector.id}" class="tag-container">
                            ${selector.column ? '' : '<span style="color: #6c757d; font-size: 10px;">请先选择表头列</span>'}
                        </div>
                    </div>
                `;
                container.appendChild(item);

                // 如果已选择列，加载对应的值
                if (selector.column) {
                    loadHeaderValues(selector.id, selector.column);
                }
            });
        }

        async function updateSelectorColumn(selectorId, column) {
            const selector = flexibleHeaderSelectors.find(s => s.id === selectorId);
            if (selector) {
                selector.column = column;
                selector.values = []; // 重置选择的值
                
                if (column) {
                    await loadHeaderValues(selectorId, column);
                } else {
                    const container = document.getElementById(`headerValues_${selectorId}`);
                    container.innerHTML = '<span style="color: #6c757d; font-size: 10px;">请先选择表头列</span>';
                }
                
                checkFormValidity();
            }
        }

        async function loadHeaderValues(selectorId, column) {
            const container = document.getElementById(`headerValues_${selectorId}`);
            
            if (!column || !fileId) {
                container.innerHTML = '<span style="color: #6c757d; font-size: 10px;">请先选择表头列</span>';
                return;
            }

            try {
                container.innerHTML = '<span style="color: #6c757d; font-size: 10px;">加载中...</span>';
                
                const response = await fetch(window.pageBridge.getApiUrl(`/unique_values/${fileId}/${encodeURIComponent(column)}`));
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                container.innerHTML = '';

                const selector = flexibleHeaderSelectors.find(s => s.id === selectorId);
                
                data.values.forEach(value => {
                    const tag = document.createElement('div');
                    tag.className = 'tag';
                    tag.textContent = value;
                    
                    // 检查是否已选中
                    if (selector && selector.values.includes(value)) {
                        tag.classList.add('selected');
                    }
                    
                    tag.onclick = () => toggleSelectorValue(selectorId, value, tag);
                    container.appendChild(tag);
                });

            } catch (error) {
                console.error('获取表头内容失败:', error);
                container.innerHTML = '<span style="color: #dc3545; font-size: 10px;">加载失败</span>';
            }
        }

        function toggleSelectorValue(selectorId, value, tagElement) {
            const selector = flexibleHeaderSelectors.find(s => s.id === selectorId);
            if (!selector) return;

            const index = selector.values.indexOf(value);
            if (index > -1) {
                selector.values.splice(index, 1);
                tagElement.classList.remove('selected');
            } else {
                selector.values.push(value);
                tagElement.classList.add('selected');
            }
            
            checkFormValidity();
        }

        function updateGlobalIndicator() {
            const indicator = document.getElementById('globalIndicator');
            if (flexibleHeaderSelectors.length === 0) {
                indicator.style.display = 'block';
            } else {
                indicator.style.display = 'none';
            }
        }

        function initializeFlexibleHeaderManager() {
            renderFlexibleHeaderSelectors();
            updateGlobalIndicator();
        }

        // 原有的函数已被灵活表头选择管理功能替代

        async function initializeVisualization() {
            try {
                showMessage('正在加载数据...', 'info');

                // 获取所有数据
                const response = await fetch(window.pageBridge.getApiUrl('/filter'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        file_id: fileId,
                        filters: {} // 不应用任何过滤器以获取所有数据
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                allData = await response.json();
                
                // 初始化灵活表头选择管理器
                initializeFlexibleHeaderManager();
                
                showMessage('数据加载完成', 'success');

            } catch (error) {
                console.error('Error loading data:', error);
                showMessage('数据加载失败: ' + error.message, 'error');
            }
        }





        function checkFormValidity() {
            const xAxis = document.getElementById('xAxis').value;
            const yAxis = document.getElementById('yAxis').value;
            const generateBtn = document.getElementById('generateChart');
            const applyStyleBtn = document.getElementById('applyStyle');

            // 检查是否有有效的灵活表头选择器（至少有一个选择器有选中的值，或者没有选择器表示全局模式）
            const hasValidSelectors = flexibleHeaderSelectors.length === 0 || 
                                    flexibleHeaderSelectors.some(selector => 
                                        selector.column && selector.values.length > 0
                                    );

            const isValid = hasValidSelectors && xAxis && yAxis;

            generateBtn.disabled = !isValid;

            // 应用样式按钮只有在图表已生成后才能使用
            const plotDiv = document.getElementById('plotDiv');
            const hasChart = plotDiv && plotDiv.querySelector('.js-plotly-plot');
            if (applyStyleBtn) {
                applyStyleBtn.disabled = !hasChart;
            }
        }

        async function applyStyleToChart() {
            try {
                showMessage('正在应用样式并重新生成图表...', 'info');

                // 直接调用生成图表函数，它会使用当前的样式设置
                await generateChart();

                showMessage('样式应用成功', 'success');

            } catch (error) {
                console.error('应用样式失败:', error);
                showMessage('应用样式失败: ' + error.message, 'error');
            }
        }

        async function generateChart() {
            console.log('=== 开始生成图表 ===');
            console.log('当前状态:', {
                fileId,
                flexibleHeaderSelectors
            });

            // 详细检查选择状态
            console.log('详细检查:');
            console.log('- 文件ID:', fileId);
            console.log('- 灵活表头选择器数量:', flexibleHeaderSelectors.length);
            flexibleHeaderSelectors.forEach((selector, index) => {
                console.log(`- 选择器${index + 1}: 列=${selector.column}, 值数量=${selector.values.length}, 值列表=`, selector.values);
            });

            const xAxis = document.getElementById('xAxis').value;
            const yAxis = document.getElementById('yAxis').value;

            console.log('- X轴变量:', xAxis);
            console.log('- Y轴变量:', yAxis);

            if (!fileId) {
                showMessage('请先选择数据文件', 'error');
                return;
            }

            // 检查是否有有效的选择器或者是全局模式
            const hasValidSelectors = flexibleHeaderSelectors.length === 0 || 
                                    flexibleHeaderSelectors.some(selector => 
                                        selector.column && selector.values.length > 0
                                    );

            if (!hasValidSelectors) {
                showMessage('请添加至少一个有效的筛选条件或使用全局模式', 'error');
                return;
            }

            if (!xAxis) {
                showMessage('请选择X轴变量', 'error');
                return;
            }

            if (!yAxis) {
                showMessage('请选择Y轴变量', 'error');
                return;
            }

            try {
                showMessage('正在生成图表...', 'info');

                // 构建过滤器对象
                const filters = {};
                flexibleHeaderSelectors.forEach(selector => {
                    if (selector.column && selector.values.length > 0) {
                        filters[selector.column] = selector.values.map(value => String(value));
                    }
                });

                console.log('构建的过滤器:', filters);

                const response = await fetch(window.pageBridge.getApiUrl('/plot_data'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        file_id: fileId,
                        filters: filters,
                        x_axis: xAxis,
                        y_axis: yAxis
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API响应错误:', response.status, errorText);
                    throw new Error(`HTTP error! status: ${response.status} ${errorText}`);
                }

                const plotData = await response.json();
                console.log('获取到的绘图数据:', plotData);
                renderChart(plotData);
                showMessage('图表生成成功', 'success');

            } catch (error) {
                console.error('Error generating chart:', error);
                showMessage('图表生成失败: ' + error.message, 'error');
            }
        }

        async function renderChart(plotData) {
            // 获取当前选择的轴变量
            const xAxis = document.getElementById('xAxis').value;
            const yAxis = document.getElementById('yAxis').value;

            // 获取详细数据以生成多条曲线
            try {
                // 构建过滤器对象
                const filters = {};
                flexibleHeaderSelectors.forEach(selector => {
                    if (selector.column && selector.values.length > 0) {
                        filters[selector.column] = selector.values.map(value => String(value));
                    }
                });

                const response = await fetch(window.pageBridge.getApiUrl('/filter'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        file_id: fileId,
                        filters: filters
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const filteredData = await response.json();
                console.log('=== API筛选结果 ===');
                console.log('筛选后的数据总行数:', filteredData.length);
                console.log('筛选后的数据样本 (前3行):', filteredData.slice(0, 3));

                // 检查数据中是否包含所需的列
                if (filteredData.length > 0) {
                    const sampleRow = filteredData[0];
                    console.log('数据列名:', Object.keys(sampleRow));
                    flexibleHeaderSelectors.forEach((selector, index) => {
                        console.log(`选择器${index + 1}列存在:`, selector.column in sampleRow);
                    });
                    console.log('X轴列存在:', xAxis in sampleRow);
                    console.log('Y轴列存在:', yAxis in sampleRow);
                }

                // 获取用户选择的样式设置
                const colorScheme = document.getElementById('colorScheme').value;
                const lineWidth = parseInt(document.getElementById('lineWidth').value);
                const markerSize = parseInt(document.getElementById('markerSize').value);
                const markerStyle = document.getElementById('markerStyle').value;

                const traces = [];
                const colors = getColorScheme(colorScheme);
                let colorIndex = 0;

                console.log('=== 开始创建曲线 ===');
                console.log('样式设置:', { colorScheme, lineWidth, markerSize, markerStyle });

                // 根据灵活表头选择器创建曲线
                if (flexibleHeaderSelectors.length === 0) {
                    // 全局模式：创建一条包含所有数据的曲线
                    console.log('\n--- 全局模式：创建单一曲线 ---');
                    
                    const xValues = filteredData.map(row => row[xAxis]).filter(v => v !== null && v !== undefined);
                    const yValues = filteredData.map(row => row[yAxis]).filter(v => v !== null && v !== undefined);
                    
                    console.log(`全局数据:`, {
                        总行数: filteredData.length,
                        X轴有效值数量: xValues.length,
                        Y轴有效值数量: yValues.length,
                        X轴样本: xValues.slice(0, 5),
                        Y轴样本: yValues.slice(0, 5)
                    });
                    
                    if (xValues.length > 0 && yValues.length > 0) {
                        const traceName = '全局数据';
                        
                        // 检查是否已有该曲线的个性化设置
                        let existingTrace = null;
                        if (currentPlot && currentPlot.traces) {
                            existingTrace = currentPlot.traces.find(t => t.name === traceName);
                        }
                        
                        const trace = {
                            x: xValues,
                            y: yValues,
                            mode: 'lines+markers',
                            type: 'scatter',
                            name: traceName,
                            line: {
                                color: existingTrace ? existingTrace.line.color : colors[0],
                                width: lineWidth
                            },
                            marker: {
                                color: existingTrace ? existingTrace.marker.color : colors[0],
                                size: markerSize,
                                symbol: existingTrace ? existingTrace.marker.symbol : markerStyle
                            }
                        };
                        traces.push(trace);
                        console.log(`✓ 成功创建全局曲线: ${trace.name}, 数据点数: ${xValues.length}`);
                        colorIndex++;
                    }
                } else {
                    // 基于选择器的模式：为每个选择器的值组合创建曲线
                    console.log('\n--- 基于选择器模式：创建多条曲线 ---');
                    
                    // 生成所有可能的组合
                    function generateCombinations(selectors, currentCombination = {}, index = 0) {
                        if (index >= selectors.length) {
                            return [currentCombination];
                        }
                        
                        const selector = selectors[index];
                        const combinations = [];
                        
                        selector.values.forEach(value => {
                            const newCombination = { ...currentCombination, [selector.column]: value };
                            combinations.push(...generateCombinations(selectors, newCombination, index + 1));
                        });
                        
                        return combinations;
                    }
                    
                    const validSelectors = flexibleHeaderSelectors.filter(s => s.column && s.values.length > 0);
                    const combinations = generateCombinations(validSelectors);
                    
                    console.log(`生成的组合数量: ${combinations.length}`);
                    
                    combinations.forEach((combination, combIndex) => {
                        console.log(`\n--- 处理组合 ${combIndex + 1}: ${JSON.stringify(combination)} ---`);
                        
                        // 根据组合筛选数据
                        const combinationData = filteredData.filter(row => {
                            return Object.keys(combination).every(column => 
                                String(row[column]) === String(combination[column])
                            );
                        });
                        
                        console.log(`匹配的数据行数:`, combinationData.length);
                        
                        if (combinationData.length > 0) {
                            const xValues = combinationData.map(row => row[xAxis]).filter(v => v !== null && v !== undefined);
                            const yValues = combinationData.map(row => row[yAxis]).filter(v => v !== null && v !== undefined);
                            
                            console.log(`提取的数据:`, {
                                原始行数: combinationData.length,
                                X轴有效值数量: xValues.length,
                                Y轴有效值数量: yValues.length,
                                X轴样本: xValues.slice(0, 5),
                                Y轴样本: yValues.slice(0, 5)
                            });
                            
                            if (xValues.length > 0 && yValues.length > 0) {
                                // 生成曲线名称
                                const traceName = Object.keys(combination)
                                    .map(column => `${column}=${combination[column]}`)
                                    .join('-');
                                
                                // 检查是否已有该曲线的个性化设置
                                let existingTrace = null;
                                if (currentPlot && currentPlot.traces) {
                                    existingTrace = currentPlot.traces.find(t => t.name === traceName);
                                }
                                
                                const trace = {
                                    x: xValues,
                                    y: yValues,
                                    mode: 'lines+markers',
                                    type: 'scatter',
                                    name: traceName,
                                    line: {
                                        color: existingTrace ? existingTrace.line.color : colors[colorIndex % colors.length],
                                        width: lineWidth
                                    },
                                    marker: {
                                        color: existingTrace ? existingTrace.marker.color : colors[colorIndex % colors.length],
                                        size: markerSize,
                                        symbol: existingTrace ? existingTrace.marker.symbol : markerStyle
                                    }
                                };
                                traces.push(trace);
                                console.log(`✓ 成功创建曲线: ${trace.name}, 数据点数: ${xValues.length}`);
                                colorIndex++;
                            } else {
                                console.log(`✗ 跳过组合 ${JSON.stringify(combination)}: X轴数据=${xValues.length}个, Y轴数据=${yValues.length}个`);
                            }
                        }
                    });
                }

                console.log('=== 绘图前最终检查 ===');
                console.log('创建的曲线数量:', traces.length);
                if (traces.length > 0) {
                    console.log('曲线详情:');
                    traces.forEach((trace, index) => {
                        console.log(`  ${index + 1}. ${trace.name}: ${trace.x.length} 个数据点`);
                    });
                } else {
                    console.log('⚠️ 没有创建任何曲线!');
                }

                if (traces.length === 0) {
                    showMessage('没有找到匹配的数据进行绘图', 'warning');
                    return;
                }

                // 获取图例位置配置
                const legendPosition = document.getElementById('legendPosition').value;
                const legendConfig = getLegendConfig(legendPosition);

                // 根据图例位置调整边距
                let margins = { t: 50, r: 50, b: 50, l: 50 };
                if (legendPosition === 'outside-right') {
                    margins.r = 150; // 为右侧图例留出更多空间
                } else if (legendPosition === 'top') {
                    margins.t = 100; // 为顶部图例留出更多空间
                } else if (legendPosition === 'bottom') {
                    margins.b = 100; // 为底部图例留出更多空间
                }

                // 获取用户自定义的标题
                const chartTitle = document.getElementById('chartTitle').value || `${xAxis}-${yAxis} 曲线图`;
                const xAxisTitle = document.getElementById('xAxisTitle').value || xAxis;
                const yAxisTitle = document.getElementById('yAxisTitle').value || yAxis;

                const layout = {
                    title: {
                        text: chartTitle,
                        font: { size: 16 }
                    },
                    xaxis: {
                        title: xAxisTitle,
                        showgrid: true,
                        gridcolor: '#e9ecef'
                    },
                    yaxis: {
                        title: yAxisTitle,
                        showgrid: true,
                        gridcolor: '#e9ecef'
                    },
                    plot_bgcolor: 'white',
                    paper_bgcolor: 'white',
                    margin: margins,
                    legend: legendConfig
                };

                console.log('图例配置:', legendConfig);
                console.log('边距配置:', margins);

                const config = {
                    responsive: true,
                    displayModeBar: true,
                    modeBarButtonsToAdd: [
                        {
                            name: 'Download PNG',
                            icon: (typeof Plotly !== 'undefined' && Plotly.Icons) ? Plotly.Icons.camera : undefined,
                            click: function(gd) {
                                if (typeof Plotly !== 'undefined') {
                                    Plotly.downloadImage(gd, {format: 'png', width: 800, height: 600, filename: 'chart'});
                                }
                            }
                        }
                    ],
                    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d']
                };

                Plotly.newPlot('plotDiv', traces, layout, config);
                currentPlot = { traces, layout, config };

                // 显示工具栏、样式控制区域和曲线控制面板
                document.getElementById('chartToolbar').style.display = 'flex';
                document.getElementById('chartControls').style.display = 'flex';
                document.getElementById('curveControlPanel').style.display = 'block';

                // 生成曲线控制项
                generateCurveControls(traces);

                // 更新按钮状态
                checkFormValidity();

            } catch (error) {
                console.error('Error rendering chart:', error);
                showMessage('图表渲染失败: ' + error.message, 'error');
            }
        }

        function resetZoom() {
            if (currentPlot && typeof Plotly !== 'undefined') {
                Plotly.relayout('plotDiv', {
                    'xaxis.autorange': true,
                    'yaxis.autorange': true
                });
            }
        }

        /**
         * 生成曲线控制项
         * @param {Array} traces - 图表曲线数据
         */
        function generateCurveControls(traces) {
            const controlList = document.getElementById('curveControlList');
            controlList.innerHTML = '';

            if (traces.length === 0) {
                controlList.innerHTML = '<div style="text-align: center; color: #6c757d; padding: 20px; font-style: italic;">暂无曲线数据</div>';
                return;
            }

            traces.forEach((trace, index) => {
                const curveItem = document.createElement('div');
                curveItem.className = 'curve-item';
                curveItem.innerHTML = `
                    <div class="curve-name" title="${trace.name}">${trace.name}</div>
                    <div class="curve-controls">
                        <input type="color"
                               class="curve-color-picker"
                               value="${trace.line.color}"
                               data-curve-index="${index}"
                               title="选择颜色">
                        <select class="curve-marker-select"
                                data-curve-index="${index}"
                                title="选择标记样式">
                            <option value="circle" ${trace.marker.symbol === 'circle' ? 'selected' : ''}>圆形 ●</option>
                            <option value="square" ${trace.marker.symbol === 'square' ? 'selected' : ''}>方形 ■</option>
                            <option value="diamond" ${trace.marker.symbol === 'diamond' ? 'selected' : ''}>菱形 ♦</option>
                            <option value="triangle-up" ${trace.marker.symbol === 'triangle-up' ? 'selected' : ''}>上三角 ▲</option>
                            <option value="triangle-down" ${trace.marker.symbol === 'triangle-down' ? 'selected' : ''}>下三角 ▼</option>
                            <option value="cross" ${trace.marker.symbol === 'cross' ? 'selected' : ''}>十字 +</option>
                            <option value="x" ${trace.marker.symbol === 'x' ? 'selected' : ''}>X形 ×</option>
                        </select>
                    </div>
                `;
                controlList.appendChild(curveItem);
            });

            // 默认展开曲线控制面板（如果之前是折叠状态）
            const content = document.getElementById('curveControlContent');
            const toggle = document.getElementById('curveControlToggle');
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                toggle.textContent = '▼';
                toggle.classList.remove('collapsed');
            }
        }

        /**
         * 切换曲线控制面板的展开/折叠状态
         */
        function toggleCurveControlPanel() {
            const content = document.getElementById('curveControlContent');
            const toggle = document.getElementById('curveControlToggle');

            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                toggle.textContent = '▼';
                toggle.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                toggle.textContent = '▶';
                toggle.classList.add('collapsed');
            }
        }

        /**
         * 重置所有曲线样式为默认值
         */
        async function resetAllCurveStyles() {
            try {
                if (!currentPlot || !currentPlot.traces) {
                    showMessage('请先生成图表', 'error');
                    return;
                }

                showMessage('正在重置曲线样式...', 'info');

                // 获取默认配色方案
                const colorScheme = document.getElementById('colorScheme').value;
                const colors = getColorScheme(colorScheme);
                const markerStyle = document.getElementById('markerStyle').value;

                // 重置所有曲线控制项
                const colorPickers = document.querySelectorAll('.curve-color-picker');
                const markerSelects = document.querySelectorAll('.curve-marker-select');

                colorPickers.forEach((picker, index) => {
                    picker.value = colors[index % colors.length];
                });

                markerSelects.forEach(select => {
                    select.value = markerStyle;
                });

                // 应用重置后的样式
                await applyCurveStyles();

                showMessage('曲线样式重置成功', 'success');

            } catch (error) {
                console.error('重置曲线样式失败:', error);
                showMessage('重置曲线样式失败: ' + error.message, 'error');
            }
        }

        /**
         * 应用曲线样式
         */
        async function applyCurveStyles() {
            try {
                showMessage('正在应用曲线样式...', 'info');

                if (!currentPlot || !currentPlot.traces) {
                    showMessage('请先生成图表', 'error');
                    return;
                }

                // 获取曲线控制面板中的设置
                const colorPickers = document.querySelectorAll('.curve-color-picker');
                const markerSelects = document.querySelectorAll('.curve-marker-select');

                // 更新traces数据
                const updatedTraces = currentPlot.traces.map((trace, index) => {
                    const newTrace = { ...trace };

                    // 应用颜色设置
                    if (colorPickers[index]) {
                        const newColor = colorPickers[index].value;
                        newTrace.line = { ...newTrace.line, color: newColor };
                        newTrace.marker = { ...newTrace.marker, color: newColor };
                    }

                    // 应用标记样式设置
                    if (markerSelects[index]) {
                        const newMarker = markerSelects[index].value;
                        newTrace.marker = { ...newTrace.marker, symbol: newMarker };
                    }

                    return newTrace;
                });

                // 重新绘制图表
                if (typeof Plotly !== 'undefined') {
                    await Plotly.redraw('plotDiv');
                    await Plotly.restyle('plotDiv', {
                        'line.color': updatedTraces.map(t => t.line.color),
                        'marker.color': updatedTraces.map(t => t.marker.color),
                        'marker.symbol': updatedTraces.map(t => t.marker.symbol)
                    });
                } else {
                    throw new Error('Plotly 库未加载');
                }

                // 更新currentPlot
                currentPlot.traces = updatedTraces;

                showMessage('曲线样式应用成功', 'success');

            } catch (error) {
                console.error('应用曲线样式失败:', error);
                showMessage('应用曲线样式失败: ' + error.message, 'error');
            }
        }

        function downloadPNG() {
            if (currentPlot) {
                Plotly.downloadImage('plotDiv', {
                    format: 'png',
                    width: 1200,
                    height: 800,
                    filename: 'daplot_chart'
                });
            }
        }

        function downloadSVG() {
            if (currentPlot) {
                Plotly.downloadImage('plotDiv', {
                    format: 'svg',
                    width: 1200,
                    height: 800,
                    filename: 'daplot_chart'
                });
            }
        }

        function showMessage(message, type) {
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.textContent = message;
            statusMessage.className = `status-message ${type}`;
            statusMessage.style.display = 'block';

            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    statusMessage.style.display = 'none';
                }, 3000);
            }
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const toggleBtn = document.querySelector('.sidebar-toggle');

            sidebar.classList.toggle('collapsed');

            if (sidebar.classList.contains('collapsed')) {
                toggleBtn.textContent = '▶';
                toggleBtn.title = '展开侧边栏';
            } else {
                toggleBtn.textContent = '◀';
                toggleBtn.title = '收起侧边栏';
            }
        }
    </script>
</body>
</html>
