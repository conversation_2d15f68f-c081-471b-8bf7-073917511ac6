<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试测试页面</title>
</head>
<body>
    <h1>调试测试页面</h1>
    <div>
        <h2>localStorage 内容:</h2>
        <div id="storage-content"></div>
    </div>
    <div>
        <button onclick="clearStorage()">清空 localStorage</button>
        <button onclick="testData()">设置测试数据</button>
        <button onclick="goToVisualization()">前往可视化页面</button>
    </div>

    <script>
        function displayStorageContent() {
            const content = document.getElementById('storage-content');
            const fileId = localStorage.getItem('fileId');
            const headers = localStorage.getItem('headers');
            
            content.innerHTML = `
                <p><strong>fileId:</strong> ${fileId || '未设置'}</p>
                <p><strong>headers:</strong> ${headers || '未设置'}</p>
            `;
        }
        
        function clearStorage() {
            localStorage.clear();
            displayStorageContent();
            alert('localStorage 已清空');
        }
        
        function testData() {
            localStorage.setItem('fileId', 'test-file-id-123');
            localStorage.setItem('headers', JSON.stringify(['列1', '列2', '列3', 'Project', 'Status']));
            displayStorageContent();
            alert('测试数据已设置');
        }
        
        function goToVisualization() {
            window.location.href = 'visualization.html';
        }
        
        // 页面加载时显示内容
        document.addEventListener('DOMContentLoaded', displayStorageContent);
    </script>
</body>
</html>